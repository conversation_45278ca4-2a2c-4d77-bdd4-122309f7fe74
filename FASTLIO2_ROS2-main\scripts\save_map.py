#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from interface.srv import SaveMaps
import sys
import os
import argparse

class MapSaver(Node):
    def __init__(self):
        super().__init__('map_saver')
        self.client = self.create_client(SaveMaps, '/pgo/save_maps')
        
    def save_map(self, file_path, save_patches=True):
        """保存地图的主要函数"""
        
        # 检查服务是否可用
        if not self.client.wait_for_service(timeout_sec=5.0):
            self.get_logger().error('PGO save_maps服务不可用！')
            self.get_logger().error('请确保PGO节点正在运行')
            return False
            
        # 创建保存目录
        try:
            os.makedirs(file_path, exist_ok=True)
            self.get_logger().info(f'创建保存目录: {file_path}')
        except Exception as e:
            self.get_logger().error(f'无法创建目录 {file_path}: {e}')
            return False
            
        # 检查目录权限
        if not os.access(file_path, os.W_OK):
            self.get_logger().error(f'没有写入权限到目录: {file_path}')
            return False
            
        # 创建服务请求
        request = SaveMaps.Request()
        request.file_path = file_path
        request.save_patches = save_patches
        
        self.get_logger().info(f'开始保存地图到: {file_path}')
        self.get_logger().info(f'保存patches: {save_patches}')
        
        # 调用服务
        try:
            future = self.client.call_async(request)
            rclpy.spin_until_future_complete(self, future, timeout_sec=30.0)
            
            if future.result() is not None:
                response = future.result()
                if response.success:
                    self.get_logger().info('地图保存成功！')
                    self.get_logger().info(f'服务响应: {response.message}')
                    self.check_saved_files(file_path, save_patches)
                    return True
                else:
                    self.get_logger().error(f'地图保存失败: {response.message}')
                    return False
            else:
                self.get_logger().error('服务调用超时或失败')
                return False
                
        except Exception as e:
            self.get_logger().error(f'服务调用异常: {e}')
            return False
            
    def check_saved_files(self, file_path, save_patches):
        """检查保存的文件"""
        self.get_logger().info('========================================')
        self.get_logger().info('保存结果检查')
        self.get_logger().info('========================================')
        
        # 检查全局地图文件
        map_file = os.path.join(file_path, 'map.pcd')
        if os.path.exists(map_file):
            size = os.path.getsize(map_file) / (1024 * 1024)  # MB
            self.get_logger().info(f'✓ 全局地图文件: {map_file}')
            self.get_logger().info(f'  文件大小: {size:.2f} MB')
        else:
            self.get_logger().warning(f'✗ 未找到全局地图文件: {map_file}')
            
        if save_patches:
            # 检查patches目录
            patches_dir = os.path.join(file_path, 'patches')
            if os.path.exists(patches_dir):
                patch_files = [f for f in os.listdir(patches_dir) if f.endswith('.pcd')]
                self.get_logger().info(f'✓ Patches目录: {patches_dir}')
                self.get_logger().info(f'  Patch文件数量: {len(patch_files)}')
            else:
                self.get_logger().warning(f'✗ 未找到patches目录: {patches_dir}')
                
            # 检查位姿文件
            poses_file = os.path.join(file_path, 'poses.txt')
            if os.path.exists(poses_file):
                with open(poses_file, 'r') as f:
                    pose_count = len(f.readlines())
                self.get_logger().info(f'✓ 位姿文件: {poses_file}')
                self.get_logger().info(f'  位姿数量: {pose_count}')
            else:
                self.get_logger().warning(f'✗ 未找到位姿文件: {poses_file}')
                
        self.get_logger().info('========================================')

def main():
    parser = argparse.ArgumentParser(description='FASTLIO2 地图保存工具')
    parser.add_argument('--path', '-p', 
                       default='/tmp/fastlio2_maps',
                       help='地图保存路径 (默认: /tmp/fastlio2_maps)')
    parser.add_argument('--no-patches', 
                       action='store_true',
                       help='不保存patch文件')
    
    args = parser.parse_args()
    
    # 初始化ROS2
    rclpy.init()
    
    try:
        # 创建地图保存节点
        map_saver = MapSaver()
        
        print("========================================")
        print("FASTLIO2 地图保存工具")
        print("========================================")
        print(f"保存路径: {args.path}")
        print(f"保存patches: {not args.no_patches}")
        print("========================================")
        
        # 保存地图
        success = map_saver.save_map(args.path, not args.no_patches)
        
        if success:
            print("地图保存完成！")
            sys.exit(0)
        else:
            print("地图保存失败！")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"程序异常: {e}")
        sys.exit(1)
    finally:
        rclpy.shutdown()

if __name__ == '__main__':
    main()
