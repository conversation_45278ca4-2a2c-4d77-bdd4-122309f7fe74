#!/usr/bin/env python3

import numpy as np
import open3d as o3d
import argparse
import os
import sys
from pathlib import Path

def fix_inverted_pointcloud(input_pcd_path, output_pcd_path, flip_z=True, flip_y=False):
    """
    修正倒装雷达产生的倒置点云地图
    
    Args:
        input_pcd_path: 输入点云文件路径
        output_pcd_path: 输出点云文件路径  
        flip_z: 是否翻转Z轴（默认True，修正倒装问题）
        flip_y: 是否翻转Y轴（默认False）
    """
    
    print(f"读取点云文件: {input_pcd_path}")
    
    # 读取点云
    try:
        pcd = o3d.io.read_point_cloud(input_pcd_path)
        if len(pcd.points) == 0:
            print("错误: 点云文件为空或无法读取")
            return False
    except Exception as e:
        print(f"错误: 无法读取点云文件 - {e}")
        return False
    
    print(f"原始点云信息:")
    print(f"  点数: {len(pcd.points)}")
    
    # 获取点云数据
    points = np.asarray(pcd.points)
    
    # 显示原始统计信息
    print(f"  X范围: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
    print(f"  Y范围: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
    print(f"  Z范围: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")
    
    # 创建变换矩阵
    transform = np.eye(4)
    
    if flip_z:
        print("应用Z轴翻转（修正倒装雷达）")
        transform[2, 2] = -1  # Z轴翻转
        
    if flip_y:
        print("应用Y轴翻转")
        transform[1, 1] = -1  # Y轴翻转
    
    # 应用变换
    pcd.transform(transform)
    
    # 获取变换后的点云数据
    points_fixed = np.asarray(pcd.points)
    
    print(f"修正后点云信息:")
    print(f"  X范围: [{points_fixed[:, 0].min():.2f}, {points_fixed[:, 0].max():.2f}]")
    print(f"  Y范围: [{points_fixed[:, 1].min():.2f}, {points_fixed[:, 1].max():.2f}]")
    print(f"  Z范围: [{points_fixed[:, 2].min():.2f}, {points_fixed[:, 2].max():.2f}]")
    
    # 保存修正后的点云
    try:
        success = o3d.io.write_point_cloud(output_pcd_path, pcd)
        if success:
            print(f"修正后的点云已保存到: {output_pcd_path}")
            return True
        else:
            print("错误: 保存点云失败")
            return False
    except Exception as e:
        print(f"错误: 保存点云时出错 - {e}")
        return False

def fix_poses_file(poses_file_path, output_poses_path, flip_z=True, flip_y=False):
    """
    修正位姿文件中的坐标
    
    格式: filename x y z qw qx qy qz
    """
    
    if not os.path.exists(poses_file_path):
        print(f"位姿文件不存在: {poses_file_path}")
        return False
        
    print(f"修正位姿文件: {poses_file_path}")
    
    try:
        with open(poses_file_path, 'r') as f:
            lines = f.readlines()
        
        fixed_lines = []
        for line in lines:
            parts = line.strip().split()
            if len(parts) >= 8:
                filename = parts[0]
                x, y, z = float(parts[1]), float(parts[2]), float(parts[3])
                qw, qx, qy, qz = float(parts[4]), float(parts[5]), float(parts[6]), float(parts[7])
                
                # 修正位置
                if flip_z:
                    z = -z
                if flip_y:
                    y = -y
                
                # 修正四元数（对应的旋转）
                if flip_z and not flip_y:
                    # 仅Z轴翻转：绕X轴旋转180度
                    qx, qy, qz = -qx, -qy, qz
                elif flip_y and not flip_z:
                    # 仅Y轴翻转：绕Z轴旋转180度  
                    qx, qy, qw = qx, -qy, -qw
                elif flip_z and flip_y:
                    # 同时翻转：绕Y轴旋转180度
                    qx, qz, qw = -qx, -qz, -qw
                
                fixed_line = f"{filename} {x:.6f} {y:.6f} {z:.6f} {qw:.6f} {qx:.6f} {qy:.6f} {qz:.6f}\n"
                fixed_lines.append(fixed_line)
            else:
                fixed_lines.append(line)
        
        with open(output_poses_path, 'w') as f:
            f.writelines(fixed_lines)
            
        print(f"修正后的位姿文件已保存到: {output_poses_path}")
        return True
        
    except Exception as e:
        print(f"错误: 处理位姿文件时出错 - {e}")
        return False

def fix_map_directory(map_dir, output_dir=None, flip_z=True, flip_y=False):
    """
    修正整个地图目录
    """
    
    map_path = Path(map_dir)
    if not map_path.exists():
        print(f"错误: 地图目录不存在 - {map_dir}")
        return False
    
    if output_dir is None:
        output_dir = str(map_path.parent / f"{map_path.name}_fixed")
    
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    print(f"修正地图目录: {map_dir} -> {output_dir}")
    
    success_count = 0
    
    # 修正主地图文件
    main_map = map_path / "map.pcd"
    if main_map.exists():
        output_map = output_path / "map.pcd"
        if fix_inverted_pointcloud(str(main_map), str(output_map), flip_z, flip_y):
            success_count += 1
    
    # 修正patches目录
    patches_dir = map_path / "patches"
    if patches_dir.exists():
        output_patches = output_path / "patches"
        output_patches.mkdir(exist_ok=True)
        
        for patch_file in patches_dir.glob("*.pcd"):
            output_patch = output_patches / patch_file.name
            if fix_inverted_pointcloud(str(patch_file), str(output_patch), flip_z, flip_y):
                success_count += 1
    
    # 修正位姿文件
    poses_file = map_path / "poses.txt"
    if poses_file.exists():
        output_poses = output_path / "poses.txt"
        if fix_poses_file(str(poses_file), str(output_poses), flip_z, flip_y):
            success_count += 1
    
    print(f"修正完成，成功处理 {success_count} 个文件")
    return success_count > 0

def main():
    parser = argparse.ArgumentParser(description='修正倒装雷达产生的倒置地图')
    parser.add_argument('input', help='输入文件或目录路径')
    parser.add_argument('--output', '-o', help='输出文件或目录路径')
    parser.add_argument('--flip-z', action='store_true', default=True, 
                       help='翻转Z轴（默认启用，修正倒装问题）')
    parser.add_argument('--no-flip-z', action='store_true', 
                       help='不翻转Z轴')
    parser.add_argument('--flip-y', action='store_true', 
                       help='同时翻转Y轴')
    
    args = parser.parse_args()
    
    # 处理flip_z参数
    flip_z = args.flip_z and not args.no_flip_z
    
    input_path = Path(args.input)
    
    if not input_path.exists():
        print(f"错误: 输入路径不存在 - {args.input}")
        sys.exit(1)
    
    print("========================================")
    print("倒装雷达地图修正工具")
    print("========================================")
    print(f"输入: {args.input}")
    print(f"翻转Z轴: {flip_z}")
    print(f"翻转Y轴: {args.flip_y}")
    print("========================================")
    
    if input_path.is_file() and input_path.suffix == '.pcd':
        # 单个点云文件
        if args.output is None:
            output_file = input_path.parent / f"{input_path.stem}_fixed{input_path.suffix}"
        else:
            output_file = Path(args.output)
        
        success = fix_inverted_pointcloud(str(input_path), str(output_file), flip_z, args.flip_y)
        sys.exit(0 if success else 1)
        
    elif input_path.is_dir():
        # 地图目录
        success = fix_map_directory(str(input_path), args.output, flip_z, args.flip_y)
        sys.exit(0 if success else 1)
        
    else:
        print("错误: 输入必须是.pcd文件或包含地图的目录")
        sys.exit(1)

if __name__ == '__main__':
    main()
