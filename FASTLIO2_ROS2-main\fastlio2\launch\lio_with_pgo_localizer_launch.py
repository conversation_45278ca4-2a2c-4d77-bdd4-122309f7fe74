#!/usr/bin/env python3

import launch
import launch_ros.actions
from launch.substitutions import PathJoinSubstitution
from launch_ros.substitutions import FindPackageShare


def generate_launch_description():
    """
    综合启动文件：同时启动FASTLIO2建图、PGO回环检测和Localizer定位功能
    
    功能说明：
    1. FASTLIO2 - 实时激光雷达惯性里程计，提供建图和里程计
    2. PGO - 位置图优化，进行回环检测和地图优化
    3. Localizer - 基于地图的定位功能
    
    话题通信关系：
    - FASTLIO2发布: /fastlio2/body_cloud, /fastlio2/lio_odom
    - PGO订阅: /fastlio2/body_cloud, /fastlio2/lio_odom
    - Localizer订阅: /fastlio2/body_cloud, /fastlio2/lio_odom
    """
    
    # 配置文件路径
    lio_config_path = PathJoinSubstitution(
        [FindPackageShare("fastlio2"), "config", "lio.yaml"]
    )
    
    pgo_config_path = PathJoinSubstitution(
        [FindPackageShare("pgo"), "config", "pgo.yaml"]
    )
    
    localizer_config_path = PathJoinSubstitution(
        [FindPackageShare("localizer"), "config", "localizer.yaml"]
    )
    
    # RViz配置文件 - 使用PGO的配置，因为它包含更多可视化内容
    rviz_cfg = PathJoinSubstitution(
        [FindPackageShare("pgo"), "rviz", "pgo.rviz"]
    )

    return launch.LaunchDescription(
        [
            # 1. 启动FASTLIO2节点 - 核心建图和里程计功能
            launch_ros.actions.Node(
                package="fastlio2",
                namespace="fastlio2",
                executable="lio_node",
                name="lio_node",
                output="screen",
                parameters=[{"config_path": lio_config_path.perform(launch.LaunchContext())}],
                remappings=[
                    # 确保话题名称正确映射
                    ("body_cloud", "/fastlio2/body_cloud"),
                    ("odom", "/fastlio2/lio_odom"),
                    ("path", "/fastlio2/path"),
                    ("world_cloud", "/fastlio2/world_cloud"),
                ]
            ),
            
            # 2. 启动PGO节点 - 回环检测和地图优化
            launch_ros.actions.Node(
                package="pgo",
                namespace="pgo",
                executable="pgo_node",
                name="pgo_node",
                output="screen",
                parameters=[{"config_path": pgo_config_path.perform(launch.LaunchContext())}]
            ),
            
            # 3. 启动Localizer节点 - 基于地图的定位
            launch_ros.actions.Node(
                package="localizer",
                namespace="localizer",
                executable="localizer_node",
                name="localizer_node",
                output="screen",
                parameters=[{"config_path": localizer_config_path.perform(launch.LaunchContext())}]
            ),
            
            # 4. 启动RViz2进行可视化
            launch_ros.actions.Node(
                package="rviz2",
                namespace="pgo",
                executable="rviz2",
                name="rviz2",
                output="screen",
                arguments=["-d", rviz_cfg.perform(launch.LaunchContext())],
            ),
        ]
    )
