#!/bin/bash

# 倒装雷达地图修正脚本
# 使用PCL工具修正Z轴翻转的点云地图

# 设置默认参数
INPUT_DIR=""
OUTPUT_DIR=""
FLIP_Z=true
FLIP_Y=false

# 显示帮助信息
show_help() {
    echo "倒装雷达地图修正工具"
    echo ""
    echo "使用方法:"
    echo "  $0 <输入目录> [输出目录]"
    echo ""
    echo "参数:"
    echo "  输入目录    包含map.pcd的地图目录"
    echo "  输出目录    修正后地图的保存目录（可选，默认为输入目录_fixed）"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  --no-flip-z    不翻转Z轴"
    echo "  --flip-y       同时翻转Y轴"
    echo ""
    echo "示例:"
    echo "  $0 /path/to/map_directory"
    echo "  $0 /path/to/map_directory /path/to/output_directory"
}

# 检查PCL工具是否可用
check_pcl_tools() {
    if ! command -v pcl_transform_point_cloud &> /dev/null; then
        echo "错误: 未找到pcl_transform_point_cloud工具"
        echo "请安装PCL工具包:"
        echo "  Ubuntu: sudo apt install pcl-tools"
        echo "  或者使用Python版本: python3 fix_inverted_map.py"
        return 1
    fi
    return 0
}

# 创建变换矩阵文件
create_transform_matrix() {
    local matrix_file="$1"
    
    if [ "$FLIP_Z" = true ] && [ "$FLIP_Y" = false ]; then
        # 仅翻转Z轴
        cat > "$matrix_file" << EOF
1 0 0 0
0 1 0 0
0 0 -1 0
0 0 0 1
EOF
    elif [ "$FLIP_Z" = false ] && [ "$FLIP_Y" = true ]; then
        # 仅翻转Y轴
        cat > "$matrix_file" << EOF
1 0 0 0
0 -1 0 0
0 0 1 0
0 0 0 1
EOF
    elif [ "$FLIP_Z" = true ] && [ "$FLIP_Y" = true ]; then
        # 同时翻转Y和Z轴
        cat > "$matrix_file" << EOF
1 0 0 0
0 -1 0 0
0 0 -1 0
0 0 0 1
EOF
    else
        # 不翻转（单位矩阵）
        cat > "$matrix_file" << EOF
1 0 0 0
0 1 0 0
0 0 1 0
0 0 0 1
EOF
    fi
}

# 变换单个点云文件
transform_pointcloud() {
    local input_file="$1"
    local output_file="$2"
    local matrix_file="$3"
    
    echo "变换点云: $(basename "$input_file")"
    
    if pcl_transform_point_cloud "$input_file" "$output_file" -matrix "$matrix_file" > /dev/null 2>&1; then
        echo "  ✓ 成功: $output_file"
        return 0
    else
        echo "  ✗ 失败: $input_file"
        return 1
    fi
}

# 修正位姿文件（简化版本，仅翻转位置坐标）
fix_poses_file() {
    local input_file="$1"
    local output_file="$2"
    
    if [ ! -f "$input_file" ]; then
        return 0
    fi
    
    echo "修正位姿文件: $(basename "$input_file")"
    
    if [ "$FLIP_Z" = true ] && [ "$FLIP_Y" = false ]; then
        # 仅翻转Z坐标
        awk '{if(NF>=8) printf "%s %.6f %.6f %.6f %.6f %.6f %.6f %.6f\n", $1, $2, $3, -$4, $5, $6, $7, $8; else print $0}' "$input_file" > "$output_file"
    elif [ "$FLIP_Z" = false ] && [ "$FLIP_Y" = true ]; then
        # 仅翻转Y坐标
        awk '{if(NF>=8) printf "%s %.6f %.6f %.6f %.6f %.6f %.6f %.6f\n", $1, $2, -$3, $4, $5, $6, $7, $8; else print $0}' "$input_file" > "$output_file"
    elif [ "$FLIP_Z" = true ] && [ "$FLIP_Y" = true ]; then
        # 同时翻转Y和Z坐标
        awk '{if(NF>=8) printf "%s %.6f %.6f %.6f %.6f %.6f %.6f %.6f\n", $1, $2, -$3, -$4, $5, $6, $7, $8; else print $0}' "$input_file" > "$output_file"
    else
        # 不修改
        cp "$input_file" "$output_file"
    fi
    
    echo "  ✓ 位姿文件已修正"
}

# 主处理函数
process_map_directory() {
    local input_dir="$1"
    local output_dir="$2"
    
    # 检查输入目录
    if [ ! -d "$input_dir" ]; then
        echo "错误: 输入目录不存在 - $input_dir"
        return 1
    fi
    
    # 创建输出目录
    mkdir -p "$output_dir"
    
    # 创建临时变换矩阵文件
    local matrix_file=$(mktemp)
    create_transform_matrix "$matrix_file"
    
    echo "========================================="
    echo "倒装雷达地图修正"
    echo "========================================="
    echo "输入目录: $input_dir"
    echo "输出目录: $output_dir"
    echo "翻转Z轴: $FLIP_Z"
    echo "翻转Y轴: $FLIP_Y"
    echo "========================================="
    
    local success_count=0
    local total_count=0
    
    # 处理主地图文件
    if [ -f "$input_dir/map.pcd" ]; then
        ((total_count++))
        if transform_pointcloud "$input_dir/map.pcd" "$output_dir/map.pcd" "$matrix_file"; then
            ((success_count++))
        fi
    fi
    
    # 处理patches目录
    if [ -d "$input_dir/patches" ]; then
        mkdir -p "$output_dir/patches"
        
        for patch_file in "$input_dir/patches"/*.pcd; do
            if [ -f "$patch_file" ]; then
                ((total_count++))
                local patch_name=$(basename "$patch_file")
                if transform_pointcloud "$patch_file" "$output_dir/patches/$patch_name" "$matrix_file"; then
                    ((success_count++))
                fi
            fi
        done
    fi
    
    # 处理位姿文件
    if [ -f "$input_dir/poses.txt" ]; then
        ((total_count++))
        if fix_poses_file "$input_dir/poses.txt" "$output_dir/poses.txt"; then
            ((success_count++))
        fi
    fi
    
    # 清理临时文件
    rm -f "$matrix_file"
    
    echo "========================================="
    echo "处理完成: $success_count/$total_count 个文件成功"
    echo "修正后的地图保存在: $output_dir"
    echo "========================================="
    
    return 0
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --no-flip-z)
            FLIP_Z=false
            shift
            ;;
        --flip-y)
            FLIP_Y=true
            shift
            ;;
        -*)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
        *)
            if [ -z "$INPUT_DIR" ]; then
                INPUT_DIR="$1"
            elif [ -z "$OUTPUT_DIR" ]; then
                OUTPUT_DIR="$1"
            else
                echo "错误: 过多的参数"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# 检查必需参数
if [ -z "$INPUT_DIR" ]; then
    echo "错误: 请指定输入目录"
    show_help
    exit 1
fi

# 设置默认输出目录
if [ -z "$OUTPUT_DIR" ]; then
    OUTPUT_DIR="${INPUT_DIR}_fixed"
fi

# 检查PCL工具
if ! check_pcl_tools; then
    exit 1
fi

# 执行处理
process_map_directory "$INPUT_DIR" "$OUTPUT_DIR"
