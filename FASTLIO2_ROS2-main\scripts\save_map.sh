#!/bin/bash

# FASTLIO2 地图保存脚本
# 使用方法: ./save_map.sh [保存目录路径] [是否保存patches]
# 示例: ./save_map.sh /home/<USER>/maps true

# 设置默认参数
DEFAULT_MAP_DIR="/tmp/fastlio2_maps"
DEFAULT_SAVE_PATCHES="true"

# 获取参数
MAP_DIR=${1:-$DEFAULT_MAP_DIR}
SAVE_PATCHES=${2:-$DEFAULT_SAVE_PATCHES}

echo "========================================="
echo "FASTLIO2 地图保存脚本"
echo "========================================="
echo "保存目录: $MAP_DIR"
echo "保存patches: $SAVE_PATCHES"
echo "========================================="

# 检查ROS2环境
if ! command -v ros2 &> /dev/null; then
    echo "错误: 未找到ros2命令，请确保ROS2环境已正确设置"
    echo "请运行: source /opt/ros/humble/setup.bash"
    echo "或者: source /your/workspace/install/setup.bash"
    exit 1
fi

# 检查PGO节点是否运行
echo "检查PGO节点状态..."
if ! ros2 node list | grep -q "pgo_node"; then
    echo "警告: 未检测到pgo_node节点运行"
    echo "请先启动PGO节点:"
    echo "ros2 launch fastlio2 lio_with_pgo_localizer_launch.py"
    echo "或者:"
    echo "ros2 launch pgo pgo_launch.py"
    read -p "是否继续尝试保存地图? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查save_maps服务是否存在
echo "检查save_maps服务..."
if ! ros2 service list | grep -q "/pgo/save_maps"; then
    echo "错误: 未找到/pgo/save_maps服务"
    echo "请确保PGO节点正在运行"
    exit 1
fi

# 创建保存目录
echo "创建保存目录: $MAP_DIR"
mkdir -p "$MAP_DIR"

if [ ! -d "$MAP_DIR" ]; then
    echo "错误: 无法创建目录 $MAP_DIR"
    exit 1
fi

# 检查目录权限
if [ ! -w "$MAP_DIR" ]; then
    echo "错误: 没有写入权限到目录 $MAP_DIR"
    exit 1
fi

echo "目录创建成功: $MAP_DIR"

# 调用保存服务
echo "开始保存地图..."
echo "调用命令: ros2 service call /pgo/save_maps interface/srv/SaveMaps \"{file_path: '$MAP_DIR', save_patches: $SAVE_PATCHES}\""

# 尝试多种调用方式
echo "尝试方式1: 标准格式"
if ros2 service call /pgo/save_maps interface/srv/SaveMaps "{file_path: '$MAP_DIR', save_patches: $SAVE_PATCHES}" 2>/dev/null; then
    echo "地图保存成功！"
else
    echo "方式1失败，尝试方式2: YAML格式"
    if ros2 service call /pgo/save_maps interface/srv/SaveMaps "file_path: '$MAP_DIR'
save_patches: $SAVE_PATCHES" 2>/dev/null; then
        echo "地图保存成功！"
    else
        echo "方式2失败，尝试方式3: 简化格式"
        if ros2 service call /pgo/save_maps interface/srv/SaveMaps "file_path: $MAP_DIR, save_patches: $SAVE_PATCHES" 2>/dev/null; then
            echo "地图保存成功！"
        else
            echo "所有方式都失败了，请检查以下内容:"
            echo "1. PGO节点是否正常运行"
            echo "2. 是否有关键帧数据"
            echo "3. interface包是否正确编译"
            echo ""
            echo "调试信息:"
            echo "服务类型: $(ros2 service type /pgo/save_maps 2>/dev/null || echo '未知')"
            echo "节点列表: $(ros2 node list | grep pgo || echo '无PGO节点')"
            exit 1
        fi
    fi
fi

# 检查保存结果
echo ""
echo "========================================="
echo "保存结果检查"
echo "========================================="

if [ -f "$MAP_DIR/map.pcd" ]; then
    echo "✓ 全局地图文件: $MAP_DIR/map.pcd"
    echo "  文件大小: $(du -h "$MAP_DIR/map.pcd" | cut -f1)"
else
    echo "✗ 未找到全局地图文件: $MAP_DIR/map.pcd"
fi

if [ "$SAVE_PATCHES" = "true" ]; then
    if [ -d "$MAP_DIR/patches" ]; then
        PATCH_COUNT=$(ls -1 "$MAP_DIR/patches"/*.pcd 2>/dev/null | wc -l)
        echo "✓ Patches目录: $MAP_DIR/patches"
        echo "  Patch文件数量: $PATCH_COUNT"
    else
        echo "✗ 未找到patches目录: $MAP_DIR/patches"
    fi
    
    if [ -f "$MAP_DIR/poses.txt" ]; then
        POSE_COUNT=$(wc -l < "$MAP_DIR/poses.txt")
        echo "✓ 位姿文件: $MAP_DIR/poses.txt"
        echo "  位姿数量: $POSE_COUNT"
    else
        echo "✗ 未找到位姿文件: $MAP_DIR/poses.txt"
    fi
fi

echo "========================================="
echo "地图保存完成！"
echo "保存位置: $MAP_DIR"
echo "========================================="
