# FASTLIO2综合功能使用说明

## 概述

本文档说明如何同时使用FASTLIO2的建图、PGO回环检测和Localizer定位功能。

## 功能模块说明

### 1. FASTLIO2 (建图模块)
- **功能**: 实时激光雷达惯性里程计，提供高精度建图和里程计
- **发布话题**: 
  - `/fastlio2/body_cloud` - 机体坐标系点云
  - `/fastlio2/lio_odom` - 里程计信息
  - `/fastlio2/world_cloud` - 世界坐标系点云
  - `/fastlio2/path` - 轨迹路径

### 2. PGO (回环检测模块)
- **功能**: 位置图优化，进行回环检测和全局地图优化
- **订阅话题**: `/fastlio2/body_cloud`, `/fastlio2/lio_odom`
- **发布话题**: `/pgo/loop_markers` - 回环检测标记

### 3. Localizer (定位模块)
- **功能**: 基于预建地图的实时定位
- **订阅话题**: `/fastlio2/body_cloud`, `/fastlio2/lio_odom`
- **服务**: 重定位服务，地图加载服务

## 启动方式

### 方式1: 完整功能启动（推荐）
同时启动建图、PGO回环检测和定位功能：

```bash
ros2 launch fastlio2 lio_with_pgo_localizer_launch.py
```

### 方式2: 建图+回环检测
仅启动建图和回环检测功能：

```bash
ros2 launch fastlio2 lio_with_pgo_launch.py
```

### 方式3: 分别启动（调试用）
```bash
# 终端1: 启动FASTLIO2建图
ros2 launch fastlio2 lio_launch.py

# 终端2: 启动PGO回环检测
ros2 launch pgo pgo_launch.py

# 终端3: 启动Localizer定位
ros2 launch localizer localizer_launch.py
```

## 使用流程

### 1. 建图阶段
```bash
# 启动完整功能
ros2 launch fastlio2 lio_with_pgo_localizer_launch.py

# 播放数据包或连接传感器
ros2 bag play your_bag_file.bag

# 创建保存目录并保存优化后的地图
mkdir -p /path/to/save/map
ros2 service call /pgo/save_maps interface/srv/SaveMaps "{file_path: '/path/to/save/map', save_patches: true}"
```

### 2. 定位阶段
```bash
# 如果已有地图，可以进行重定位
ros2 service call /localizer/relocalize interface/srv/Relocalize "{pcd_path: '/path/to/map.pcd', x: 0.0, y: 0.0, z: 0.0, yaw: 0.0, pitch: 0.0, roll: 0.0}"

# 检查重定位结果
ros2 service call /localizer/relocalize_check interface/srv/IsValid "{code: 0}"
```

## 话题通信关系

```
传感器数据 → FASTLIO2 → PGO
                ↓        ↓
              点云/里程计  回环优化
                ↓        ↓
            Localizer ← 优化地图
                ↓
            定位结果
```

## 配置文件说明

### FASTLIO2配置 (lio.yaml)
- 传感器话题配置
- IMU-LiDAR外参
- 建图参数
- RANSAC点云分割参数

### PGO配置 (pgo.yaml)
- 关键帧选择参数
- 回环检测阈值
- 地图优化参数

### Localizer配置 (localizer.yaml)
- ICP配准参数
- 自动重定位配置
- 定位更新频率

## 注意事项

1. **命名空间**: 各模块使用不同的命名空间避免冲突
   - FASTLIO2: `/fastlio2/`
   - PGO: `/pgo/`
   - Localizer: `/localizer/`

2. **资源消耗**: 同时运行三个模块会消耗较多计算资源，建议：
   - 使用性能较好的计算机
   - 根据需要调整各模块的更新频率
   - 监控CPU和内存使用情况

3. **数据流**: 确保传感器数据质量良好，特别是：
   - IMU数据频率稳定
   - 激光雷达数据完整
   - 时间戳同步准确

4. **地图保存**: 建图完成后及时保存地图：
   ```bash
   # 注意：file_path必须是已存在的目录路径，不是文件路径
   mkdir -p /your/map/directory
   ros2 service call /pgo/save_maps interface/srv/SaveMaps "{file_path: '/your/map/directory', save_patches: true}"
   ```

## 故障排除

### 常见问题

1. **话题连接问题**
   ```bash
   # 检查话题发布情况
   ros2 topic list
   ros2 topic echo /fastlio2/body_cloud
   ```

2. **TF变换问题**
   ```bash
   # 检查TF树
   ros2 run tf2_tools view_frames
   ```

3. **性能问题**
   - 降低点云发布频率
   - 调整ICP迭代次数
   - 减少关键帧密度

4. **重定位失败**
   - 检查地图文件路径
   - 确认初始位置估计准确
   - 调整ICP参数

5. **save_maps服务调用失败**
   ```bash
   # 错误: "passed service type is invalid"
   # 解决方案:

   # 1. 检查服务是否存在
   ros2 service list | grep save_maps

   # 2. 检查服务类型
   ros2 service type /pgo/save_maps

   # 3. 确保目录存在（重要！）
   mkdir -p /your/map/directory

   # 4. 正确的调用方式
   ros2 service call /pgo/save_maps interface/srv/SaveMaps "{file_path: '/your/map/directory', save_patches: true}"

   # 5. 如果仍然失败，尝试不同的语法
   ros2 service call /pgo/save_maps interface/srv/SaveMaps "file_path: '/your/map/directory'
save_patches: true"
   ```

6. **服务类型不匹配问题**
   ```bash
   # 检查interface包是否正确编译
   ros2 interface show interface/srv/SaveMaps

   # 重新编译interface包
   cd /path/to/workspace
   colcon build --packages-select interface
   source install/setup.bash
   ```

## 扩展功能

可以根据需要添加其他功能模块：
- Octomap生成
- 路径规划
- 障碍物检测
- 语义分割

通过修改launch文件可以灵活组合不同的功能模块。
