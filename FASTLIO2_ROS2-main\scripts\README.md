# FASTLIO2 脚本工具

本目录包含用于FASTLIO2系统的实用脚本工具。

## 地图保存工具

### 1. save_map.sh (Bash脚本)

**功能**: 通过命令行调用PGO服务保存地图

**使用方法**:
```bash
# 给脚本执行权限
chmod +x save_map.sh

# 使用默认参数保存
./save_map.sh

# 指定保存路径
./save_map.sh /home/<USER>/my_maps

# 指定保存路径和是否保存patches
./save_map.sh /home/<USER>/my_maps true
```

**特点**:
- 自动检查ROS2环境
- 验证PGO节点状态
- 创建保存目录
- 多种服务调用方式尝试
- 详细的结果检查

### 2. save_map.py (Python脚本)

**功能**: 通过ROS2 Python API保存地图

**使用方法**:
```bash
# 使用默认参数
python3 save_map.py

# 指定保存路径
python3 save_map.py --path /home/<USER>/my_maps

# 不保存patch文件
python3 save_map.py --no-patches

# 查看帮助
python3 save_map.py --help
```

**特点**:
- 原生ROS2 Python接口
- 更好的错误处理
- 详细的日志输出
- 文件验证功能

## 故障排除

### 常见问题

1. **"passed service type is invalid"错误**
   - 确保interface包已正确编译
   - 检查ROS2环境设置
   - 使用Python脚本进行调试

2. **"PGO节点未运行"错误**
   ```bash
   # 检查节点状态
   ros2 node list | grep pgo
   
   # 启动PGO节点
   ros2 launch fastlio2 lio_with_pgo_localizer_launch.py
   ```

3. **"目录权限"错误**
   ```bash
   # 检查目录权限
   ls -la /path/to/save/directory
   
   # 修改权限
   chmod 755 /path/to/save/directory
   ```

4. **"无关键帧数据"错误**
   - 确保已运行建图过程
   - 检查传感器数据输入
   - 验证FASTLIO2节点状态

### 调试步骤

1. **检查服务可用性**:
   ```bash
   ros2 service list | grep save_maps
   ros2 service type /pgo/save_maps
   ```

2. **检查节点状态**:
   ```bash
   ros2 node list
   ros2 topic list | grep pgo
   ```

3. **手动测试服务**:
   ```bash
   # 创建测试目录
   mkdir -p /tmp/test_map
   
   # 调用服务
   ros2 service call /pgo/save_maps interface/srv/SaveMaps "{file_path: '/tmp/test_map', save_patches: true}"
   ```

## 输出文件说明

保存成功后，指定目录将包含以下文件：

### 必需文件
- **map.pcd**: 全局优化后的完整点云地图

### 可选文件 (当save_patches=true时)
- **patches/**: 目录，包含各个关键帧的点云文件
  - `0.pcd`, `1.pcd`, ... : 各关键帧的点云数据
- **poses.txt**: 关键帧位姿文件
  - 格式: `文件名 x y z qw qx qy qz`

## 使用建议

1. **推荐使用Python脚本** - 更稳定的错误处理和调试信息
2. **定期保存地图** - 在长时间建图过程中定期保存
3. **检查磁盘空间** - 确保有足够空间保存大型点云文件
4. **备份重要地图** - 将保存的地图文件备份到安全位置

## 倒装雷达地图修正工具

### 3. fix_inverted_map.py (Python脚本)

**功能**: 修正倒装雷达产生的倒置点云地图

**使用方法**:
```bash
# 修正整个地图目录
python3 fix_inverted_map.py /path/to/map_directory

# 指定输出目录
python3 fix_inverted_map.py /path/to/map_directory --output /path/to/fixed_map

# 修正单个点云文件
python3 fix_inverted_map.py map.pcd --output map_fixed.pcd

# 不翻转Z轴
python3 fix_inverted_map.py /path/to/map --no-flip-z

# 同时翻转Y轴
python3 fix_inverted_map.py /path/to/map --flip-y
```

**特点**:
- 使用Open3D库进行精确的点云变换
- 支持整个地图目录或单个文件
- 自动修正位姿文件中的坐标
- 详细的处理日志和统计信息

### 4. fix_inverted_map.sh (Bash脚本)

**功能**: 使用PCL工具修正倒置地图

**使用方法**:
```bash
# 给脚本执行权限
chmod +x fix_inverted_map.sh

# 修正地图目录
./fix_inverted_map.sh /path/to/map_directory

# 指定输出目录
./fix_inverted_map.sh /path/to/map_directory /path/to/fixed_map

# 查看帮助
./fix_inverted_map.sh --help
```

**特点**:
- 基于PCL命令行工具
- 轻量级，无需Python依赖
- 支持批量处理
- 自动创建变换矩阵

### 倒装雷达问题说明

**问题现象**:
- RViz中Z轴（蓝色）指向下方
- 保存的地图点云Z坐标为负值
- 地面显示在Z=0以下

**影响**:
- 定位功能坐标系不匹配
- 导航高度信息错误
- 传感器融合问题

**解决方案**:
1. 使用修正工具翻转Z轴
2. 重新保存修正后的地图
3. 在定位时使用修正后的地图

## 扩展功能

可以基于这些脚本开发更多功能：
- 自动地图压缩
- 地图质量评估
- 批量地图处理
- 地图格式转换
- 坐标系变换验证
- 地图可视化工具
