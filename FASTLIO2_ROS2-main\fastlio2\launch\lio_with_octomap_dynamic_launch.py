#!/usr/bin/env python3

import launch
import launch_ros.actions
from launch.substitutions import PathJoinSubstitution
from launch_ros.substitutions import FindPackageShare

def generate_launch_description():
    # FASTLIO2配置
    rviz_cfg = PathJoinSubstitution(
        [FindPackageShare("fastlio2"), "rviz", "fastlio2.rviz"]
    )

    config_path = PathJoinSubstitution(
        [FindPackageShare("fastlio2"), "config", "lio.yaml"]
    )

    return launch.LaunchDescription(
        [
            # 启动FASTLIO2节点
            launch_ros.actions.Node(
                package="fastlio2",
                executable="lio_node",
                name="lio_node",
                output="screen",
                parameters=[{"config_path": config_path}]
            ),
            
            # 启动octomap服务器 - 优化动态障碍物清除
            launch_ros.actions.Node(
                package='octomap_server2',
                executable='octomap_server',
                name='octomap_server',
                output='screen',
                remappings=[('cloud_in', 'cluster_clouds')],
                parameters=[{
                    'resolution': 0.05,
                    'frame_id': 'map',
                    'base_frame_id': 'odom',
                    'height_map': True,
                    'colored_map': True,
                    'color_factor': 0.8,
                    'filter_ground': False,
                    'filter_speckles': True,
                    'ground_filter/distance': 0.04,
                    'ground_filter/angle': 0.15,
                    'ground_filter/plane_distance': 0.07,
                    'compress_map': True,
                    'incremental_2D_projection': True,
                    'sensor_model/max_range': 30.0,
                    # 动态障碍物清除优化参数
                    'sensor_model/hit': 0.6,      # 降低hit概率，减少误检
                    'sensor_model/miss': 0.45,    # 提高miss概率，加快清除
                    'sensor_model/min': 0.1,      # 降低最小阈值
                    'sensor_model/max': 0.9,      # 降低最大阈值
                    'color/r': 0.0,
                    'color/g': 0.0,
                    'color/b': 1.0,
                    'color/a': 1.0,
                    'color_free/r': 0.0,
                    'color_free/g': 1.0,
                    'color_free/b': 0.0,
                    'color_free/a': 1.0,
                    'publish_free_space': True,   # 发布自由空间信息
                    'latch': False,               # 不锁存，允许实时更新
                    'track_changes': True         # 跟踪变化
                }]
            ),
            
            # 启动RViz2
            launch_ros.actions.Node(
                package="rviz2",
                executable="rviz2",
                name="rviz2",
                output="screen",
                arguments=["-d", rviz_cfg],
            ),
        ]
    )
