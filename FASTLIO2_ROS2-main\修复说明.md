# FASTLIO2 + Octomap 问题修复说明

## 问题描述
1. 坐标系方向完全不对齐
2. 实际运行轨迹与显示轨迹不符（转弯走了几米后倒车回到起点）
3. 利用octomap_ros2生成的栅格地图完全不能更新

## 修复方案

### 1. 坐标系对齐问题修复

**问题原因：**
- 雷达倒置安装需要特殊的IMU-LiDAR外参配置
- TF变换中存在不必要的旋转修正

**修复措施：**
- 修改 `FASTLIO2_ROS2-main/fastlio2/config/lio.yaml`：
  ```yaml
  # 雷达倒置安装的外参配置（X轴180度翻转）
  r_il: [1.0, 0.0, 0.0, 0.0, -1.0, 0.0, 0.0, 0.0, -1.0]
  t_il: [-0.011, -0.02329, 0.04412]
  ```
- 修改 `FASTLIO2_ROS2-main/fastlio2/src/lio_node.cpp`：
  - 保留雷达倒置的旋转修正：`m_lidar_flip = Eigen::AngleAxisd(M_PI, Eigen::Vector3d::UnitX());`
  - 移除其他不必要的旋转修正（m_odom_rotation, m_map_rotation）
  - 简化TF发布函数，直接使用IESKF估计的位姿

### 2. 轨迹估计问题修复

**问题原因：**
- 雷达倒置安装导致重力对齐不可靠
- IMU初始化样本数量不足

**修复措施：**
- 修改 `FASTLIO2_ROS2-main/fastlio2/config/lio.yaml`：
  ```yaml
  gravity_align: false  # 禁用重力对齐，因为雷达倒置
  imu_init_num: 50      # 从20增加到50
  ```

### 3. Octomap地图更新问题修复

**问题原因：**
- octomap订阅的话题名称不正确
- 配置参数不适合已分割的点云数据
- 需要启用2D栅格地图生成供导航使用

**修复措施：**
- 修改 `octomap_ros2/launch/octomap_server_launch.py`：
  ```python
  DeclareLaunchArgument('input_cloud_topic', default_value='ground_cloud')
  DeclareLaunchArgument('height_map', default_value='True')  # 启用2D栅格地图
  DeclareLaunchArgument('incremental_2D_projection', default_value='True')  # 启用增量2D投影
  DeclareLaunchArgument('filter_speckles', default_value='True')
  # 优化传感器模型参数
  DeclareLaunchArgument('sensor_model/max_range', default_value='30.0')
  DeclareLaunchArgument('sensor_model/hit', default_value='0.8')
  DeclareLaunchArgument('sensor_model/miss', default_value='0.3')
  ```

### 4. 代码重构和优化

**重命名和清理：**
- 将 `GridMapProcessor` 重命名为 `PointCloudProcessor` 以避免混淆
- 移除FASTLIO2中不必要的栅格地图生成代码
- 保持FASTLIO2专注于点云分割（RANSAC + 欧式聚类）
- 让octomap_ros2负责栅格地图生成

### 5. 动态障碍物清除问题修复

**问题描述：**
- 动态障碍物（如人）通过后在栅格地图上留下黑色残影
- 地图不会自动清除已经移走的障碍物

**解决方案：**
- 调整octomap传感器模型参数以加快动态障碍物清除：
  ```python
  'sensor_model/hit': 0.6,      # 降低hit概率，减少误检
  'sensor_model/miss': 0.45,    # 提高miss概率，加快清除
  'sensor_model/min': 0.1,      # 降低最小阈值
  'sensor_model/max': 0.9,      # 降低最大阈值
  'publish_free_space': True,   # 发布自由空间信息
  'latch': False,               # 不锁存，允许实时更新
  'track_changes': True         # 跟踪变化
  ```

### 6. 创建综合启动文件

创建了两个启动文件：
- `lio_with_octomap_launch.py` - 标准配置
- `lio_with_octomap_dynamic_launch.py` - 动态障碍物优化配置

## 使用方法

### 方法1：标准配置（推荐）
```bash
ros2 launch fastlio2 lio_with_octomap_launch.py
```

### 方法2：动态障碍物优化配置（解决残影问题）
```bash
ros2 launch fastlio2 lio_with_octomap_dynamic_launch.py
```

### 方法3：分别启动
```bash
# 终端1：启动FASTLIO2
ros2 launch fastlio2 lio_launch.py

# 终端2：启动octomap
ros2 launch octomap_server2 octomap_server_launch.py
```

## 配置特点

1. **雷达倒置安装支持**：正确配置了IMU-LiDAR外参和坐标系变换
2. **使用RANSAC点云分割**：已在lio.yaml中启用，符合用户偏好
3. **欧式聚类**：配置了聚类参数，用于障碍物检测
4. **分离职责**：FASTLIO2负责点云分割，octomap_ros2负责栅格地图生成
5. **优化的传感器模型**：调整了octomap的传感器参数以获得更好的地图更新性能
6. **禁用重力对齐**：因为雷达倒置安装，重力对齐不够可靠

## 预期效果

1. **坐标系对齐**：考虑雷达倒置安装的坐标系应该正确对齐
2. **准确轨迹估计**：显示的轨迹应该与实际机器人运动一致
3. **实时地图更新**：octomap应该能够实时更新3D占用栅格地图和2D栅格地图
4. **导航兼容**：生成的2D栅格地图可以直接用于导航模块

## 注意事项

1. **雷达倒置安装**：确保外参配置正确
2. **重力对齐**：禁用重力对齐后，需要确保IMU初始化足够稳定
3. **时间同步**：确保所有传感器数据的时间戳同步
4. **TF树**：检查TF树是否完整（map->odom->base_link）
5. **动态障碍物**：如果有动态障碍物残影问题，使用动态优化配置
6. **传感器模型调优**：
   - `hit`值越低，越不容易将噪声标记为障碍物
   - `miss`值越高，越快清除消失的障碍物
   - 可根据实际环境调整这些参数

## 发布的话题

- `/ground_cloud` - 地面点云
- `/cluster_clouds` - 聚类结果（推荐用于octomap输入）
- `/octomap_full` - 完整的3D八叉树地图
- `/projected_map` - 2D栅格地图（供导航使用）
- `/octomap_point_cloud_centers` - 八叉树节点中心点云
